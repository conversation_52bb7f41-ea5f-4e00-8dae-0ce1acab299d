"use client";

import { useState } from "react";
import AuthModal from "@/components/AuthModal";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Code, BarChart3, Zap } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

const features = [
  {
    icon: Code,
    title: "Easy API & SDKs",
    description:
      "Plug AdMesh into your GPT, extension, or tool with a few lines of code. RESTful, lightweight, fast.",
  },
  {
    icon: Zap,
    title: "Instant Payouts",
    description:
      "Get paid in real-time for every verified click, install, or purchase triggered by your agent.",
  },
  {
    icon: BarChart3,
    title: "Built-In Trust System",
    description:
      "Ad<PERSON>esh automatically detects fraud, scores trust, and manages rate limits — so you don’t have to.",
  },
];

export default function ForAgents() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  return (
    <section
      id="for-agents"
      className="w-full bg-gradient-to-b from-indigo-100 to-blue-50 py-32 overflow-hidden"
    >
      <div className="mx-auto max-w-7xl px-6">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          viewport={{ once: true, amount: 0.2 }}
        >
          <h2 className="text-4xl sm:text-5xl font-extrabold text-gray-800 tracking-tight mb-4">
            Monetize Your AI Agent in Minutes
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Whether you&apos;re building GPTs, Chrome extensions, or custom tools — AdMesh gives you plug-and-earn monetization with verified results.
          </p>
        </motion.div>

        {/* Feature Cards */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          variants={{
            visible: {
              transition: { staggerChildren: 0.12, delayChildren: 0.1 },
            },
          }}
          viewport={{ once: true, amount: 0.2 }}
        >
          {features.map((feature, idx) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.035 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                <Card className="h-full border-2 border-transparent transition-all duration-200 hover:border-indigo-500 hover:shadow-xl">
                  <CardHeader className="space-y-4 text-center">
                    <motion.div
                      className="bg-indigo-200 w-14 h-14 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-md"
                      whileHover={{
                        rotate: [0, -6, 6, -3, 3, 0],
                        transition: { duration: 0.4 },
                      }}
                    >
                      <Icon className="text-indigo-600 h-7 w-7" />
                    </motion.div>
                    <CardTitle className="text-xl font-semibold text-gray-900">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        {/* CTA Button */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.04 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true, amount: 0.2 }}
        >
          <Button
            onClick={() => setIsAuthModalOpen(true)}
            className="group bg-gradient-to-r from-indigo-500 to-indigo-700 text-white hover:brightness-110 transition-all duration-200 text-lg px-8 py-4 shadow-md"
            size="lg"
          >
            Get Started
          </Button>
        </motion.div>

        <AuthModal open={isAuthModalOpen} onClose={() => setIsAuthModalOpen(false)} defaultRole="agent" />
      </div>
    </section>
  );
}
