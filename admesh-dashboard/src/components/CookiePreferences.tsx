"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON> } from "lucide-react";

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export default function CookiePreferences() {
  const pathname = usePathname();
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  // Define landing pages where cookie banner should be shown
  const landingPages = ['/', '/brands', '/agents', '/users'];
  const isLandingPage = landingPages.includes(pathname);
  const isDashboard = pathname.startsWith('/dashboard');

  // Check if user has made a choice
  useEffect(() => {
    const hasConsent = localStorage.getItem("admesh-cookie-consent");
    if (!hasConsent && isLandingPage) {
      // Show banner after a short delay, only on landing pages
      const timer = setTimeout(() => setShowBanner(true), 1500);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      const savedPreferences = localStorage.getItem("admesh-cookie-preferences");
      if (savedPreferences) {
        setPreferences(JSON.parse(savedPreferences));
      }
    }
  }, [isLandingPage]);

  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem("admesh-cookie-consent", "true");
    localStorage.setItem("admesh-cookie-preferences", JSON.stringify(prefs));
    setPreferences(prefs);
    setShowBanner(false);
    setShowModal(false);
    
    // Apply preferences (you can extend this to actually control cookies)
    if (prefs.analytics) {
      // Enable analytics cookies
      console.log("Analytics cookies enabled");
    }
    if (prefs.marketing) {
      // Enable marketing cookies
      console.log("Marketing cookies enabled");
    }
    if (prefs.functional) {
      // Enable functional cookies
      console.log("Functional cookies enabled");
    }
  };

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    savePreferences(allAccepted);
  };

  const acceptNecessaryOnly = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    };
    savePreferences(necessaryOnly);
  };

  const openPreferencesModal = () => {
    setShowModal(true);
    setShowBanner(false);
  };

  const handlePreferenceChange = (key: keyof CookiePreferences, value: boolean) => {
    if (key === "necessary") return; // Cannot disable necessary cookies
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const saveCustomPreferences = () => {
    savePreferences(preferences);
  };

  // Cookie banner - only show on landing pages
  if (showBanner && isLandingPage) {
    return (
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
        <Card className="border border-gray-200 shadow-2xl bg-white">
          <CardContent className="p-6">
            <div className="flex items-start gap-3 mb-4">
              <Cookie className="w-5 h-5 text-gray-700 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900 mb-1">We use cookies</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  We use cookies to improve your experience and analyze site usage.
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={acceptAll}
                className="flex-1 bg-black hover:bg-gray-800 text-white text-sm h-9"
              >
                Accept All
              </Button>
              <Button
                onClick={openPreferencesModal}
                variant="outline"
                className="flex-1 text-sm h-9"
              >
                Customize
              </Button>
              <Button
                onClick={acceptNecessaryOnly}
                variant="ghost"
                className="flex-1 text-sm h-9"
              >
                Decline
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show cookie settings link only if user has made a choice and not on dashboard
  const hasConsent = typeof window !== 'undefined' && localStorage.getItem("admesh-cookie-consent");

  return (
    <>
      {/* Minimal cookie settings button - only show if user has consented and not on dashboard */}
      {!showBanner && hasConsent && !isDashboard && (
        <div className="fixed bottom-4 right-4 z-40">
          <Button
            onClick={() => setShowModal(true)}
            variant="ghost"
            size="sm"
            className="bg-white/90 backdrop-blur-sm shadow-sm border border-gray-200 hover:bg-white text-xs text-gray-600 hover:text-gray-900 px-3 py-1.5 h-auto"
          >
            Cookie Settings
          </Button>
        </div>
      )}

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5" />
              Cookie Preferences
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            <p className="text-sm text-gray-600">
              Manage your cookie preferences. You can change these settings at any time.
            </p>

            <div className="space-y-3">
              {/* Necessary Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Necessary</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Essential for website functionality
                  </p>
                </div>
                <Switch
                  checked={true}
                  disabled={true}
                  className="ml-4"
                />
              </div>

              {/* Analytics Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Analytics</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Help us understand site usage
                  </p>
                </div>
                <Switch
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => handlePreferenceChange("analytics", checked)}
                  className="ml-4"
                />
              </div>

              {/* Functional Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Functional</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Enhanced features and personalization
                  </p>
                </div>
                <Switch
                  checked={preferences.functional}
                  onCheckedChange={(checked) => handlePreferenceChange("functional", checked)}
                  className="ml-4"
                />
              </div>

              {/* Marketing Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Marketing</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Personalized ads and content
                  </p>
                </div>
                <Switch
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => handlePreferenceChange("marketing", checked)}
                  className="ml-4"
                />
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                onClick={saveCustomPreferences}
                className="flex-1 bg-black hover:bg-gray-800 text-white"
              >
                Save Preferences
              </Button>
              <Button
                onClick={acceptAll}
                variant="outline"
                className="flex-1"
              >
                Accept All
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
